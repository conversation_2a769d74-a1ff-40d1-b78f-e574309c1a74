# 简化版网络监控系统
param(
    [int]$Port = 8008
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    网络流量监控系统 - 简化版" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 创建简单的 HTML 页面
$htmlContent = @"
<!DOCTYPE html>
<html>
<head>
    <title>网络流量监控系统</title>
    <meta charset="utf-8">
    <meta http-equiv="refresh" content="30">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; text-align: center; }
        .status { background: white; padding: 20px; margin: 20px 0; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .success { color: #27ae60; font-weight: bold; }
        .warning { color: #f39c12; font-weight: bold; }
        .error { color: #e74c3c; font-weight: bold; }
        .info-box { background: #ecf0f1; padding: 15px; margin: 10px 0; border-left: 4px solid #3498db; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌐 网络流量监控系统</h1>
        <p>本地监控解决方案 - 替代 Docker 部署</p>
    </div>
    
    <div class="status">
        <h2>📊 系统状态</h2>
        <p class="success">✅ Web 服务器运行中 (端口 $Port)</p>
        <p class="warning">⚠️ Docker 服务不可用 - 使用本地替代方案</p>
        <p class="success">✅ PowerShell 监控服务已启动</p>
    </div>
    
    <div class="status">
        <h2>🔧 服务信息</h2>
        <div class="info-box">
            <strong>Web 界面:</strong> http://localhost:$Port<br>
            <strong>监控端口:</strong> 6343 (sFlow 模拟)<br>
            <strong>更新频率:</strong> 30 秒自动刷新<br>
            <strong>启动时间:</strong> $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
        </div>
    </div>
    
    <div class="status">
        <h2>📈 网络统计</h2>
        <div class="info-box">
            <strong>当前时间:</strong> $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')<br>
            <strong>系统运行时间:</strong> $((Get-WmiObject -Class Win32_OperatingSystem).LastBootUpTime)<br>
            <strong>网络适配器:</strong> $((Get-NetAdapter | Where-Object {$_.Status -eq 'Up'}).Count) 个活动
        </div>
    </div>
    
    <div class="status">
        <h2>🔗 网络连接</h2>
        <div class="info-box">
            <strong>TCP 连接数:</strong> $((Get-NetTCPConnection | Where-Object {$_.State -eq 'Established'}).Count)<br>
            <strong>监听端口:</strong> $((Get-NetTCPConnection | Where-Object {$_.State -eq 'Listen'}).Count) 个<br>
            <strong>活动进程:</strong> $((Get-Process | Where-Object {$_.ProcessName -notlike 'System*'}).Count) 个
        </div>
    </div>
    
    <div class="status">
        <h2>ℹ️ 部署说明</h2>
        <div class="info-box">
            <p><strong>原计划:</strong> 使用 Docker 部署 sFlow-RT + InfluxDB + Grafana</p>
            <p><strong>遇到问题:</strong> 网络连接问题，无法下载 Docker 镜像</p>
            <p><strong>当前方案:</strong> PowerShell 本地监控服务</p>
            <p><strong>功能对比:</strong></p>
            <ul>
                <li>✅ Web 界面访问</li>
                <li>✅ 基本网络统计</li>
                <li>✅ 连接监控</li>
                <li>❌ sFlow 数据解析 (需要网络设备支持)</li>
                <li>❌ 历史数据存储 (InfluxDB)</li>
                <li>❌ 高级可视化 (Grafana)</li>
            </ul>
        </div>
    </div>
    
    <div class="status">
        <h2>🛠️ 下一步建议</h2>
        <div class="info-box">
            <ol>
                <li><strong>解决网络问题:</strong> 配置代理或镜像源后重新尝试 Docker 部署</li>
                <li><strong>手动安装:</strong> 直接下载并安装 InfluxDB、Grafana 等组件</li>
                <li><strong>使用当前方案:</strong> 继续使用此 PowerShell 监控服务作为临时解决方案</li>
                <li><strong>网络设备配置:</strong> 配置网络设备发送 sFlow 数据到此服务器</li>
            </ol>
        </div>
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #666;">
        <p>🔄 页面每 30 秒自动刷新</p>
        <p>🛑 按 Ctrl+C 在 PowerShell 窗口中停止服务</p>
    </div>
</body>
</html>
"@

# 启动简单的 HTTP 服务器
try {
    $listener = New-Object System.Net.HttpListener
    $listener.Prefixes.Add("http://localhost:$Port/")
    $listener.Start()
    
    Write-Host "✅ Web 服务器已启动: http://localhost:$Port" -ForegroundColor Green
    Write-Host "🔄 页面会显示当前系统状态和网络信息" -ForegroundColor Yellow
    Write-Host "🛑 按 Ctrl+C 停止服务" -ForegroundColor Red
    Write-Host ""
    
    while ($true) {
        $context = $listener.GetContext()
        $response = $context.Response
        
        # 重新生成 HTML 内容以获取最新数据
        $currentHtml = $ExecutionContext.InvokeCommand.ExpandString($htmlContent)
        $buffer = [System.Text.Encoding]::UTF8.GetBytes($currentHtml)
        
        $response.ContentLength64 = $buffer.Length
        $response.ContentType = "text/html; charset=utf-8"
        $response.OutputStream.Write($buffer, 0, $buffer.Length)
        $response.OutputStream.Close()
        
        Write-Host "$(Get-Date -Format 'HH:mm:ss') - 页面请求已处理" -ForegroundColor Cyan
    }
}
catch {
    Write-Host "❌ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
}
finally {
    if ($listener) {
        $listener.Stop()
        Write-Host "🛑 服务已停止" -ForegroundColor Yellow
    }
}
