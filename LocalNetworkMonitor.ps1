# 本地网络监控系统 - 完全替代 Docker 方案
# 使用 PowerShell 和 Windows 本地工具实现网络流量监控

param(
    [int]$WebPort = 8008,
    [int]$SFlowPort = 6343,
    [string]$DataPath = ".\monitor_data"
)

# 创建数据目录
if (!(Test-Path $DataPath)) {
    New-Item -ItemType Directory -Path $DataPath -Force | Out-Null
}

# 全局变量
$Global:MonitorData = @()
$Global:IsRunning = $true
$Global:StartTime = Get-Date

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    本地网络流量监控系统" -ForegroundColor Cyan
Write-Host "    替代 Docker 部署方案" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# 网络统计收集函数
function Get-NetworkStatistics {
    try {
        # 获取网络适配器统计
        $adapters = Get-NetAdapterStatistics | Where-Object { $_.Name -notlike "*Loopback*" -and $_.Name -notlike "*Teredo*" }
        
        $totalStats = @{
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            TotalBytesReceived = ($adapters | Measure-Object -Property BytesReceived -Sum).Sum
            TotalBytesSent = ($adapters | Measure-Object -Property BytesSent -Sum).Sum
            ActiveAdapters = $adapters.Count
            AdapterDetails = @()
        }
        
        foreach ($adapter in $adapters) {
            $totalStats.AdapterDetails += @{
                Name = $adapter.Name
                BytesReceived = $adapter.BytesReceived
                BytesSent = $adapter.BytesSent
                PacketsReceived = $adapter.PacketsReceived
                PacketsSent = $adapter.PacketsSent
            }
        }
        
        return $totalStats
    }
    catch {
        Write-Warning "获取网络统计失败: $($_.Exception.Message)"
        return $null
    }
}

# 获取网络连接信息
function Get-NetworkConnections {
    try {
        $connections = Get-NetTCPConnection | Where-Object { $_.State -eq "Established" }
        
        $connectionStats = @{
            TotalConnections = $connections.Count
            UniqueRemoteIPs = ($connections | Select-Object -ExpandProperty RemoteAddress -Unique).Count
            ConnectionsByPort = @{}
            TopRemoteIPs = @()
        }
        
        # 按端口分组
        $portGroups = $connections | Group-Object LocalPort
        foreach ($group in $portGroups) {
            $connectionStats.ConnectionsByPort[$group.Name] = $group.Count
        }
        
        # 按远程IP分组，获取前10个
        $ipGroups = $connections | Group-Object RemoteAddress | Sort-Object Count -Descending | Select-Object -First 10
        foreach ($group in $ipGroups) {
            $connectionStats.TopRemoteIPs += @{
                IP = $group.Name
                Connections = $group.Count
            }
        }
        
        return $connectionStats
    }
    catch {
        Write-Warning "获取连接信息失败: $($_.Exception.Message)"
        return @{}
    }
}

# 模拟 sFlow 数据收集
function Start-SFlowSimulation {
    Write-Host "启动 sFlow 模拟收集器 (端口 $SFlowPort)..." -ForegroundColor Green
    
    # 创建 UDP 监听器
    try {
        $udpClient = New-Object System.Net.Sockets.UdpClient($SFlowPort)
        $endpoint = New-Object System.Net.IPEndPoint([System.Net.IPAddress]::Any, $SFlowPort)
        
        Write-Host "sFlow 收集器已启动，监听端口 $SFlowPort" -ForegroundColor Green
        
        # 在后台作业中运行
        $sflowJob = Start-Job -ScriptBlock {
            param($port)
            $udpClient = New-Object System.Net.Sockets.UdpClient($port)
            $endpoint = New-Object System.Net.IPEndPoint([System.Net.IPAddress]::Any, $port)
            
            while ($true) {
                try {
                    $data = $udpClient.Receive([ref]$endpoint)
                    Write-Output "收到 sFlow 数据: $($endpoint.Address) -> $($data.Length) 字节"
                }
                catch {
                    Start-Sleep -Seconds 1
                }
            }
        } -ArgumentList $SFlowPort
        
        return $sflowJob
    }
    catch {
        Write-Warning "无法启动 sFlow 监听器: $($_.Exception.Message)"
        Write-Host "将使用模拟数据模式" -ForegroundColor Yellow
        return $null
    }
}

# 数据收集主循环
function Start-DataCollection {
    Write-Host "开始数据收集..." -ForegroundColor Green
    
    while ($Global:IsRunning) {
        try {
            $networkStats = Get-NetworkStatistics
            $connections = Get-NetworkConnections
            
            if ($networkStats) {
                $dataPoint = @{
                    Timestamp = $networkStats.Timestamp
                    NetworkStats = $networkStats
                    Connections = $connections
                    SystemInfo = @{
                        Uptime = (Get-Date) - $Global:StartTime
                        ProcessCount = (Get-Process).Count
                        MemoryUsage = [math]::Round((Get-WmiObject -Class Win32_OperatingSystem).TotalVisibleMemorySize / 1MB, 2)
                    }
                }
                
                $Global:MonitorData += $dataPoint
                
                # 保持最近50条记录
                if ($Global:MonitorData.Count -gt 50) {
                    $Global:MonitorData = $Global:MonitorData[-50..-1]
                }
                
                # 保存到文件
                $dataPoint | ConvertTo-Json -Depth 5 | Out-File -FilePath "$DataPath\latest_data.json" -Encoding UTF8
                
                Write-Host "$(Get-Date -Format 'HH:mm:ss') - 数据收集完成" -ForegroundColor Cyan
            }
            
            Start-Sleep -Seconds 10
        }
        catch {
            Write-Warning "数据收集出错: $($_.Exception.Message)"
            Start-Sleep -Seconds 15
        }
    }
}

# Web 服务器
function Start-WebServer {
    Write-Host "启动 Web 服务器 (端口 $WebPort)..." -ForegroundColor Green
    
    try {
        $listener = New-Object System.Net.HttpListener
        $listener.Prefixes.Add("http://localhost:$WebPort/")
        $listener.Start()
        
        Write-Host "✅ Web 服务器已启动: http://localhost:$WebPort" -ForegroundColor Green
        
        while ($Global:IsRunning) {
            try {
                $context = $listener.GetContext()
                $request = $context.Request
                $response = $context.Response
                
                $path = $request.Url.AbsolutePath
                
                switch ($path) {
                    "/" { 
                        $html = Get-MonitorDashboard
                        $buffer = [System.Text.Encoding]::UTF8.GetBytes($html)
                        $response.ContentType = "text/html; charset=utf-8"
                    }
                    "/api/data" {
                        $json = $Global:MonitorData | ConvertTo-Json -Depth 5
                        $buffer = [System.Text.Encoding]::UTF8.GetBytes($json)
                        $response.ContentType = "application/json; charset=utf-8"
                    }
                    "/api/latest" {
                        $latest = if ($Global:MonitorData.Count -gt 0) { $Global:MonitorData[-1] } else { @{} }
                        $json = $latest | ConvertTo-Json -Depth 5
                        $buffer = [System.Text.Encoding]::UTF8.GetBytes($json)
                        $response.ContentType = "application/json; charset=utf-8"
                    }
                    default {
                        $response.StatusCode = 404
                        $buffer = [System.Text.Encoding]::UTF8.GetBytes("404 - 页面未找到")
                        $response.ContentType = "text/plain; charset=utf-8"
                    }
                }
                
                $response.ContentLength64 = $buffer.Length
                $response.OutputStream.Write($buffer, 0, $buffer.Length)
                $response.OutputStream.Close()
                
                Write-Host "$(Get-Date -Format 'HH:mm:ss') - $($request.HttpMethod) $path" -ForegroundColor Gray
            }
            catch {
                if ($Global:IsRunning) {
                    Write-Warning "Web 请求处理出错: $($_.Exception.Message)"
                }
            }
        }
        
        $listener.Stop()
    }
    catch {
        Write-Error "Web 服务器启动失败: $($_.Exception.Message)"
    }
}

# 生成监控仪表板
function Get-MonitorDashboard {
    $latestData = if ($Global:MonitorData.Count -gt 0) { $Global:MonitorData[-1] } else { $null }
    
    return @"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地网络监控系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: #f5f7fa; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
        .container { max-width: 1200px; margin: 20px auto; padding: 0 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stat-value { font-size: 2em; font-weight: bold; color: #667eea; margin-bottom: 5px; }
        .stat-label { color: #666; font-size: 0.9em; }
        .chart-container { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .status-indicator { display: inline-block; width: 10px; height: 10px; background: #2ecc71; border-radius: 50%; margin-right: 8px; animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        .connection-list { max-height: 200px; overflow-y: auto; }
        .connection-item { padding: 8px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; }
        .refresh-btn { background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px 0; }
        .refresh-btn:hover { background: #5a6fd8; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌐 本地网络监控系统</h1>
        <p><span class="status-indicator"></span>PowerShell 本地监控服务 | 替代 Docker 方案</p>
        <p>启动时间: $($Global:StartTime.ToString('yyyy-MM-dd HH:mm:ss')) | 运行时长: $((Get-Date) - $Global:StartTime | ForEach-Object { "{0:D2}:{1:D2}:{2:D2}" -f $_.Hours, $_.Minutes, $_.Seconds })</p>
    </div>
    
    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">$(if ($latestData) { [math]::Round(($latestData.NetworkStats.TotalBytesReceived + $latestData.NetworkStats.TotalBytesSent) / 1MB, 2) } else { "0" })</div>
                <div class="stat-label">总流量 (MB)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">$(if ($latestData) { $latestData.Connections.TotalConnections } else { "0" })</div>
                <div class="stat-label">活动连接</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">$(if ($latestData) { $latestData.NetworkStats.ActiveAdapters } else { "0" })</div>
                <div class="stat-label">网络适配器</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">$($Global:MonitorData.Count)</div>
                <div class="stat-label">数据点</div>
            </div>
        </div>
        
        <div class="chart-container">
            <h3>📊 实时网络统计</h3>
            <button class="refresh-btn" onclick="location.reload()">🔄 刷新数据</button>
            $(if ($latestData) {
                "<p><strong>最后更新:</strong> $($latestData.Timestamp)</p>"
                "<p><strong>接收字节:</strong> $([math]::Round($latestData.NetworkStats.TotalBytesReceived / 1MB, 2)) MB</p>"
                "<p><strong>发送字节:</strong> $([math]::Round($latestData.NetworkStats.TotalBytesSent / 1MB, 2)) MB</p>"
                "<p><strong>唯一远程IP:</strong> $($latestData.Connections.UniqueRemoteIPs)</p>"
            } else {
                "<p>正在收集数据...</p>"
            })
        </div>
        
        <div class="chart-container">
            <h3>🔗 活动连接 (Top 10)</h3>
            <div class="connection-list">
                $(if ($latestData -and $latestData.Connections.TopRemoteIPs) {
                    $latestData.Connections.TopRemoteIPs | ForEach-Object {
                        "<div class='connection-item'><span>$($_.IP)</span><span>$($_.Connections) 连接</span></div>"
                    }
                } else {
                    "<div class='connection-item'>暂无连接数据</div>"
                })
            </div>
        </div>
        
        <div class="chart-container">
            <h3>ℹ️ 系统信息</h3>
            <p><strong>服务端口:</strong></p>
            <ul>
                <li>Web 界面: $WebPort</li>
                <li>sFlow 收集: $SFlowPort</li>
            </ul>
            <p><strong>数据存储:</strong> $DataPath</p>
            <p><strong>状态:</strong> ✅ 正常运行</p>
            <p><strong>说明:</strong> 这是 Docker 部署失败后的本地替代方案，提供基本的网络监控功能。</p>
        </div>
    </div>
    
    <script>
        // 自动刷新
        setTimeout(() => location.reload(), 30000);
        console.log('本地网络监控系统已加载');
    </script>
</body>
</html>
"@
}

# 主函数
function Main {
    Write-Host ""
    Write-Host "正在启动本地网络监控系统..." -ForegroundColor Yellow
    Write-Host "Web 端口: $WebPort" -ForegroundColor Cyan
    Write-Host "sFlow 端口: $SFlowPort" -ForegroundColor Cyan
    Write-Host "数据目录: $DataPath" -ForegroundColor Cyan
    Write-Host ""
    
    # 启动 sFlow 模拟
    $sflowJob = Start-SFlowSimulation
    
    # 启动数据收集（后台作业）
    $dataJob = Start-Job -ScriptBlock ${function:Start-DataCollection}
    
    Write-Host "✅ 系统启动完成!" -ForegroundColor Green
    Write-Host "📊 Web 界面: http://localhost:$WebPort" -ForegroundColor Green
    Write-Host "🔌 sFlow 端口: $SFlowPort" -ForegroundColor Green
    Write-Host "💾 数据目录: $DataPath" -ForegroundColor Green
    Write-Host "🛑 按 Ctrl+C 停止服务" -ForegroundColor Red
    Write-Host ""
    
    try {
        # 启动 Web 服务器（主线程）
        Start-WebServer
    }
    finally {
        Write-Host "正在停止服务..." -ForegroundColor Yellow
        $Global:IsRunning = $false
        
        if ($dataJob) {
            Stop-Job $dataJob -ErrorAction SilentlyContinue
            Remove-Job $dataJob -ErrorAction SilentlyContinue
        }
        
        if ($sflowJob) {
            Stop-Job $sflowJob -ErrorAction SilentlyContinue
            Remove-Job $sflowJob -ErrorAction SilentlyContinue
        }
        
        Write-Host "✅ 服务已停止" -ForegroundColor Green
    }
}

# 启动系统
Main
