# Network Traffic Monitoring System

This project implements a real-time network traffic monitoring and visualization system using sFlow-RT, InfluxDB, and Grafana. The system provides insights into network traffic patterns, including geolocation data of traffic sources and destinations.

## Architecture

```
sFlow Agents → sFlow-RT (Real-time Processing) → InfluxDB (Time-series Storage) → Grafana (Visualization)
      ↑
  Network Devices (Routers/Switches)
```

## Features

- Real-time network traffic monitoring
- IP geolocation using ip2region
- Traffic analysis by source/destination country
- Protocol distribution visualization
- Historical data storage and analysis
- Web-based dashboard

## Prerequisites

- Docker and Docker Compose
- sFlow-enabled network devices
- At least 4GB of RAM (8GB recommended)

## Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd network-monitoring
   ```

2. **Initialize the environment**
   ```bash
   # On Linux/Mac
   chmod +x init.sh
   ./init.sh
   
   # On Windows, run in Git Bash or WSL
   # or manually create directories and download ip2region.xdb
   ```

3. **Start the services**
   ```bash
   docker-compose up -d
   ```

4. **Access the dashboards**
   - <PERSON>ana: http://localhost:3000 (admin/admin)
   - sFlow-RT: http://localhost:8008
   - InfluxDB: http://localhost:8086

## Configuration

### sFlow Agent Configuration

Configure your network devices to send sFlow data to the sFlow-RT instance:

```
sflow collector <SERVER_IP> vrf default
sflow agent-ip <AGENT_IP>
sflow sampling-rate 1000
sflow polling-interval 20
sflow max-datagram-size 1400
sflow enable
```

### Grafana Configuration

1. Log in to Grafana at http://localhost:3000
2. The default credentials are:
   - Username: admin
   - Password: admin
3. The dashboard should be pre-configured with the InfluxDB data source

## Components

### sFlow-RT
- Processes sFlow data in real-time
- Performs IP geolocation
- Sends processed data to InfluxDB

### InfluxDB
- Stores time-series network data
- Provides query capabilities for historical analysis

### Grafana
- Visualizes network traffic data
- Provides interactive dashboards
- Supports alerting

## Advanced Configuration

### Customizing Geolocation
To use a different geolocation database:

1. Replace `sflow-rt/ip2region.xdb` with your preferred database
2. Update the `getGeoLocation` function in `sflow-rt/init.js`

### Adding More Dashboards
1. Create new dashboards in Grafana
2. Save them to the `grafana/provisioning/dashboards/` directory
3. They will be automatically loaded on startup

## Troubleshooting

### Common Issues

1. **No data in Grafana**
   - Verify sFlow agents are sending data to sFlow-RT
   - Check sFlow-RT logs: `docker logs sflow-rt`
   - Verify InfluxDB is receiving data

2. **High CPU/Memory Usage**
   - Increase Docker resources in Docker Desktop settings
   - Reduce sFlow sampling rate on network devices
   - Limit the number of flows being tracked in sFlow-RT

## License

MIT

## Acknowledgments

- sFlow-RT: https://sflow-rt.com/
- InfluxDB: https://www.influxdata.com/
- Grafana: https://grafana.com/
- ip2region: https://github.com/lionsoul2014/ip2region
