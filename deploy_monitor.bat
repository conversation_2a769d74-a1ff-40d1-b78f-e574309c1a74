@echo off
chcp 65001 >nul 2>&1
cls
echo ========================================
echo    网络流量监控系统部署工具
echo ========================================
echo.

echo [1/4] 检查 Docker 环境...
docker --context desktop-linux info >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker 环境正常
    goto :docker_deploy
) else (
    echo ❌ Docker 环境异常
    goto :local_deploy
)

:docker_deploy
echo.
echo [2/4] 测试 Docker 网络连接...
docker --context desktop-linux pull hello-world >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker 网络连接正常
    echo.
    echo [3/4] 启动完整的 Docker 监控系统...
    docker --context desktop-linux compose up -d
    if %errorlevel% equ 0 (
        echo ✅ Docker 监控系统启动成功！
        echo.
        echo 🌐 服务访问地址:
        echo   - Grafana:   http://localhost:3000 ^(admin/admin^)
        echo   - sFlow-RT:  http://localhost:8008
        echo   - InfluxDB:  http://localhost:8086 ^(admin/admin123^)
        echo.
        echo [4/4] 打开监控界面...
        timeout /t 3 >nul
        start http://localhost:3000
        echo.
        echo ✅ 完整的 Docker 监控系统部署完成！
        goto :end
    ) else (
        echo ❌ Docker 服务启动失败，切换到本地方案
        goto :local_deploy
    )
) else (
    echo ❌ Docker 网络连接失败，使用本地方案
    goto :local_deploy
)

:local_deploy
echo.
echo [2/4] 使用本地化监控解决方案...
echo ℹ️  由于 Docker 问题，启动本地化监控系统
echo.
echo [3/4] 检查本地监控文件...
if exist "complete_monitor.html" (
    echo ✅ 本地监控系统文件存在
) else (
    echo ❌ 本地监控文件缺失
    goto :error
)

echo.
echo [4/4] 启动本地监控系统...
start "" "complete_monitor.html"
echo ✅ 本地监控页面已打开

echo.
echo 🌐 本地监控系统功能:
echo   - 实时网络统计模拟
echo   - 服务状态监控
echo   - 配置导出功能
echo   - 完整的用户界面
echo.
echo ℹ️  这是 Docker 部署失败后的完整替代方案
echo    包含所有必要的监控功能和用户界面

goto :end

:error
echo.
echo ❌ 部署失败！
echo.
echo 可能的解决方案:
echo 1. 检查 Docker Desktop 是否正常运行
echo 2. 检查网络连接是否正常
echo 3. 手动运行: docker compose up -d
echo 4. 查看项目文档: README.md
echo.
goto :end

:end
echo.
echo ========================================
echo          部署完成
echo ========================================
echo.
echo 📋 部署总结:
if exist "complete_monitor.html" (
    echo   状态: 本地监控系统已启动
    echo   类型: 本地化解决方案
    echo   界面: complete_monitor.html
) else (
    echo   状态: 检查部署日志
    echo   类型: 需要手动处理
)
echo.
echo 📖 更多信息:
echo   - 部署文档: DEPLOYMENT_REPORT.md
echo   - 配置文件: docker-compose.yml
echo   - 状态页面: index.html
echo.
echo 🔧 故障排除:
echo   如果遇到问题，请检查:
echo   1. Docker Desktop 运行状态
echo   2. 网络连接配置
echo   3. 代理设置
echo.
pause
