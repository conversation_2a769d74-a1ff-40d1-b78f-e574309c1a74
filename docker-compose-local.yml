# Docker Compose 文件 - 本地构建版本
# 使用本地构建的镜像替代外部镜像

services:
  # 本地 Web 服务器替代 sFlow-RT
  sflow-simulator:
    build:
      context: .
      dockerfile: Dockerfile.sflow
    container_name: sflow-simulator
    ports:
      - "8008:8008"
      - "6343:6343/udp"
    volumes:
      - ./sflow-rt:/app/data
    networks:
      - netmon
    restart: unless-stopped

  # 本地数据存储替代 InfluxDB
  data-storage:
    build:
      context: .
      dockerfile: Dockerfile.storage
    container_name: data-storage
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
    environment:
      - INFLUXDB_DB=netmon
      - INFLUXDB_ADMIN_USER=admin
      - INFLUXDB_ADMIN_PASSWORD=admin123
    networks:
      - netmon
    restart: unless-stopped

  # 本地可视化服务替代 Grafana
  visualization:
    build:
      context: .
      dockerfile: Dockerfile.viz
    container_name: visualization
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    depends_on:
      - data-storage
    networks:
      - netmon
    restart: unless-stopped

volumes:
  influxdb_data:
    driver: local
  grafana_data:
    driver: local

networks:
  netmon:
    driver: bridge
