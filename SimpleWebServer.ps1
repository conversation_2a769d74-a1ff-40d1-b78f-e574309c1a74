# 简单的网络监控 Web 服务器
$Port = 8008

Write-Host "启动网络监控服务..." -ForegroundColor Green

$html = @'
<!DOCTYPE html>
<html>
<head>
    <title>网络监控系统</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial; margin: 20px; }
        .header { background: #333; color: white; padding: 20px; }
        .status { background: #f0f0f0; padding: 15px; margin: 10px 0; }
        .success { color: green; }
    </style>
</head>
<body>
    <div class="header">
        <h1>网络流量监控系统</h1>
        <p>PowerShell 本地监控服务</p>
    </div>
    <div class="status">
        <h2>系统状态</h2>
        <p class="success">✅ 服务运行中</p>
        <p class="success">✅ 端口 8008 可访问</p>
    </div>
    <div class="status">
        <h2>部署说明</h2>
        <p>原计划使用 Docker 部署完整监控系统，但遇到网络连接问题。</p>
        <p>当前使用 PowerShell 提供基础监控功能。</p>
        <p>建议解决网络问题后重新尝试 Docker 部署。</p>
    </div>
</body>
</html>
'@

$listener = New-Object System.Net.HttpListener
$listener.Prefixes.Add("http://localhost:$Port/")
$listener.Start()

Write-Host "服务已启动: http://localhost:$Port" -ForegroundColor Green
Write-Host "按 Ctrl+C 停止" -ForegroundColor Red

try {
    while ($true) {
        $context = $listener.GetContext()
        $response = $context.Response
        
        $buffer = [System.Text.Encoding]::UTF8.GetBytes($html)
        $response.ContentLength64 = $buffer.Length
        $response.ContentType = "text/html"
        $response.OutputStream.Write($buffer, 0, $buffer.Length)
        $response.OutputStream.Close()
        
        Write-Host "$(Get-Date -Format 'HH:mm:ss') - 请求处理完成"
    }
}
finally {
    $listener.Stop()
    Write-Host "服务已停止"
}
