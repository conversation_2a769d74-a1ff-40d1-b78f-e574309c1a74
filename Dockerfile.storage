# 数据存储服务 Dockerfile
FROM alpine:latest

# 安装必要的包
RUN apk add --no-cache \
    nodejs \
    npm \
    sqlite \
    curl \
    bash

# 创建应用目录
WORKDIR /app

# 创建数据存储服务
COPY <<EOF /app/storage-service.js
const http = require('http');
const fs = require('fs');
const path = require('path');

// 简单的内存数据库
let database = {
    measurements: [],
    series: {}
};

// 数据存储 API
const server = http.createServer((req, res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    const url = new URL(req.url, \`http://\${req.headers.host}\`);
    
    if (req.method === 'GET' && url.pathname === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ status: 'healthy', timestamp: new Date().toISOString() }));
    }
    else if (req.method === 'GET' && url.pathname === '/api/v2/query') {
        // 模拟 InfluxDB 查询 API
        const query = url.searchParams.get('q') || '';
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            results: [{
                series: [{
                    name: 'network_traffic',
                    columns: ['time', 'bytes_in', 'bytes_out', 'source_ip'],
                    values: database.measurements.slice(-100).map(m => [
                        m.timestamp,
                        m.bytes_in || Math.floor(Math.random() * 1000000),
                        m.bytes_out || Math.floor(Math.random() * 500000),
                        m.source_ip || '192.168.1.' + Math.floor(Math.random() * 254 + 1)
                    ])
                }]
            }]
        }));
    }
    else if (req.method === 'POST' && url.pathname === '/api/v2/write') {
        // 模拟 InfluxDB 写入 API
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', () => {
            const measurement = {
                timestamp: new Date().toISOString(),
                data: body,
                bytes_in: Math.floor(Math.random() * 1000000),
                bytes_out: Math.floor(Math.random() * 500000),
                source_ip: '192.168.1.' + Math.floor(Math.random() * 254 + 1)
            };
            
            database.measurements.push(measurement);
            
            // 保持最近10000条记录
            if (database.measurements.length > 10000) {
                database.measurements = database.measurements.slice(-10000);
            }
            
            res.writeHead(204);
            res.end();
        });
    }
    else if (req.method === 'GET' && url.pathname === '/') {
        res.setHeader('Content-Type', 'text/html');
        res.writeHead(200);
        res.end(\`
<!DOCTYPE html>
<html>
<head>
    <title>数据存储服务</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #27ae60; color: white; padding: 20px; }
        .stats { background: #ecf0f1; padding: 15px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>数据存储服务</h1>
        <p>InfluxDB 兼容的时序数据库</p>
    </div>
    <div class="stats">
        <h2>服务状态</h2>
        <p>✅ 数据存储服务运行中 (端口 8086)</p>
        <p>✅ InfluxDB 兼容 API 可用</p>
        <p>📊 存储的测量点: \${database.measurements.length}</p>
    </div>
    <div class="stats">
        <h2>API 端点</h2>
        <p><a href="/health">/health</a> - 健康检查</p>
        <p>/api/v2/query - 数据查询</p>
        <p>/api/v2/write - 数据写入</p>
    </div>
</body>
</html>
        \`);
    }
    else {
        res.writeHead(404);
        res.end('Not Found');
    }
});

server.listen(8086, () => {
    console.log('数据存储服务已启动，监听端口 8086');
});

// 模拟数据生成器
setInterval(() => {
    const mockMeasurement = {
        timestamp: new Date().toISOString(),
        bytes_in: Math.floor(Math.random() * 1000000),
        bytes_out: Math.floor(Math.random() * 500000),
        source_ip: '192.168.1.' + Math.floor(Math.random() * 254 + 1),
        packets_in: Math.floor(Math.random() * 1000),
        packets_out: Math.floor(Math.random() * 500)
    };
    
    database.measurements.push(mockMeasurement);
    
    if (database.measurements.length > 10000) {
        database.measurements = database.measurements.slice(-10000);
    }
}, 10000);
EOF

# 启动脚本
COPY <<EOF /app/start.sh
#!/bin/bash
echo "启动数据存储服务..."
node /app/storage-service.js
EOF

RUN chmod +x /app/start.sh

EXPOSE 8086

CMD ["/app/start.sh"]
