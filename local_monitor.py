#!/usr/bin/env python3
"""
本地网络流量监控系统
替代 Docker 部署的轻量级解决方案
"""

import socket
import struct
import time
import json
import threading
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import sqlite3
import os

class SFlowCollector:
    """sFlow 数据收集器"""
    
    def __init__(self, port=6343):
        self.port = port
        self.running = False
        self.data_store = []
        
    def start(self):
        """启动 sFlow 收集器"""
        self.running = True
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.sock.bind(('0.0.0.0', self.port))
        print(f"sFlow 收集器已启动，监听端口 {self.port}")
        
        while self.running:
            try:
                data, addr = self.sock.recvfrom(1500)
                self.process_sflow_packet(data, addr)
            except Exception as e:
                if self.running:
                    print(f"处理 sFlow 数据包时出错: {e}")
                    
    def process_sflow_packet(self, data, addr):
        """处理 sFlow 数据包"""
        try:
            # 简化的 sFlow 解析
            timestamp = datetime.now().isoformat()
            flow_record = {
                'timestamp': timestamp,
                'source_ip': addr[0],
                'packet_size': len(data),
                'raw_data': data.hex()[:100]  # 只保存前100个字符
            }
            self.data_store.append(flow_record)
            
            # 保持最近1000条记录
            if len(self.data_store) > 1000:
                self.data_store = self.data_store[-1000:]
                
            print(f"收到 sFlow 数据包: {addr[0]} -> {len(data)} 字节")
            
        except Exception as e:
            print(f"解析 sFlow 数据包失败: {e}")
    
    def stop(self):
        """停止收集器"""
        self.running = False
        if hasattr(self, 'sock'):
            self.sock.close()

class WebInterface(BaseHTTPRequestHandler):
    """Web 界面处理器"""
    
    def do_GET(self):
        """处理 GET 请求"""
        if self.path == '/':
            self.serve_dashboard()
        elif self.path == '/api/data':
            self.serve_api_data()
        elif self.path == '/api/status':
            self.serve_status()
        else:
            self.send_error(404)
    
    def serve_dashboard(self):
        """提供仪表板页面"""
        html = """
<!DOCTYPE html>
<html>
<head>
    <title>网络流量监控系统</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-box { background: #ecf0f1; padding: 15px; border-radius: 5px; flex: 1; }
        .data-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .data-table th { background: #34495e; color: white; }
        .refresh-btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌐 网络流量监控系统</h1>
        <p>本地轻量级监控解决方案 - 替代 Docker 部署</p>
    </div>
    
    <div class="stats">
        <div class="stat-box">
            <h3>📊 系统状态</h3>
            <p>sFlow 收集器: <span id="collector-status">运行中</span></p>
            <p>监听端口: 6343</p>
        </div>
        <div class="stat-box">
            <h3>📈 数据统计</h3>
            <p>收到数据包: <span id="packet-count">0</span></p>
            <p>最后更新: <span id="last-update">--</span></p>
        </div>
        <div class="stat-box">
            <h3>🔧 服务端口</h3>
            <p>Web 界面: 8008</p>
            <p>sFlow 收集: 6343</p>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="refreshData()">🔄 刷新数据</button>
    
    <table class="data-table" id="data-table">
        <thead>
            <tr>
                <th>时间戳</th>
                <th>源 IP</th>
                <th>数据包大小</th>
                <th>原始数据 (前50字符)</th>
            </tr>
        </thead>
        <tbody id="data-body">
            <tr><td colspan="4">正在加载数据...</td></tr>
        </tbody>
    </table>
    
    <script>
        function refreshData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    updateTable(data);
                    document.getElementById('packet-count').textContent = data.length;
                    document.getElementById('last-update').textContent = new Date().toLocaleString();
                })
                .catch(error => {
                    console.error('获取数据失败:', error);
                    document.getElementById('data-body').innerHTML = '<tr><td colspan="4">获取数据失败</td></tr>';
                });
        }
        
        function updateTable(data) {
            const tbody = document.getElementById('data-body');
            if (data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4">暂无数据</td></tr>';
                return;
            }
            
            tbody.innerHTML = data.slice(-20).reverse().map(record => `
                <tr>
                    <td>${record.timestamp}</td>
                    <td>${record.source_ip}</td>
                    <td>${record.packet_size} 字节</td>
                    <td>${record.raw_data.substring(0, 50)}...</td>
                </tr>
            `).join('');
        }
        
        // 自动刷新
        setInterval(refreshData, 5000);
        refreshData();
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_api_data(self):
        """提供 API 数据"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        # 获取收集器数据
        data = getattr(self.server, 'collector_data', [])
        self.wfile.write(json.dumps(data).encode('utf-8'))
    
    def serve_status(self):
        """提供状态信息"""
        status = {
            'collector_running': True,
            'web_port': 8008,
            'sflow_port': 6343,
            'data_count': len(getattr(self.server, 'collector_data', [])),
            'timestamp': datetime.now().isoformat()
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(status).encode('utf-8'))

def main():
    """主函数"""
    print("🚀 启动本地网络流量监控系统...")
    
    # 创建 sFlow 收集器
    collector = SFlowCollector()
    
    # 在后台线程中启动收集器
    collector_thread = threading.Thread(target=collector.start, daemon=True)
    collector_thread.start()
    
    # 创建 Web 服务器
    server = HTTPServer(('localhost', 8008), WebInterface)
    server.collector_data = collector.data_store
    
    print("✅ 系统启动完成!")
    print("📊 Web 界面: http://localhost:8008")
    print("🔌 sFlow 端口: 6343")
    print("⚠️  注意: 这是 Docker 部署的替代方案")
    print("🛑 按 Ctrl+C 停止服务")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        collector.stop()
        server.shutdown()
        print("✅ 服务已停止")

if __name__ == "__main__":
    main()
