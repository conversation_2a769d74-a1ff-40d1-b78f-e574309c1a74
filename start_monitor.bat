@echo off
echo ========================================
echo    网络流量监控系统启动器
echo ========================================
echo.

echo 正在检查 Docker 网络连接...
docker --context desktop-linux pull hello-world >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker 网络连接正常
    echo 启动完整的 Docker 监控系统...
    echo.
    docker --context desktop-linux compose up -d
    if %errorlevel% equ 0 (
        echo.
        echo ✅ Docker 监控系统启动成功！
        echo.
        echo 服务访问地址:
        echo - Grafana: http://localhost:3000 ^(admin/admin^)
        echo - sFlow-RT: http://localhost:8008
        echo - InfluxDB: http://localhost:8086
        echo.
        echo 正在打开 Grafana...
        start http://localhost:3000
    ) else (
        echo ❌ Docker 服务启动失败
        goto :local_fallback
    )
) else (
    echo ❌ Docker 网络连接失败
    goto :local_fallback
)
goto :end

:local_fallback
echo.
echo 使用本地监控替代方案...
echo 正在启动本地网络监控页面...
echo.
if exist "local_network_monitor.html" (
    start "" "local_network_monitor.html"
    echo ✅ 本地监控页面已打开
    echo.
    echo 功能说明:
    echo - 实时网络统计监控
    echo - 连接状态分析
    echo - 数据导出功能
    echo - 自动刷新 ^(30秒^)
) else (
    echo ❌ 本地监控页面不存在
    if exist "index.html" (
        start "" "index.html"
        echo ✅ 状态页面已打开
    )
)

:end
echo.
echo 监控系统已启动完成
pause
