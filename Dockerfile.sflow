# sFlow 模拟器 Dockerfile
FROM alpine:latest

# 安装必要的包
RUN apk add --no-cache \
    nodejs \
    npm \
    python3 \
    py3-pip \
    curl \
    bash

# 创建应用目录
WORKDIR /app

# 创建 sFlow 模拟器
COPY <<EOF /app/sflow-simulator.js
const http = require('http');
const dgram = require('dgram');
const fs = require('fs');

// sFlow 数据收集器
const sflowServer = dgram.createSocket('udp4');
const sflowData = [];

sflowServer.on('message', (msg, rinfo) => {
    const timestamp = new Date().toISOString();
    const flowRecord = {
        timestamp: timestamp,
        source: rinfo.address,
        port: rinfo.port,
        size: msg.length,
        data: msg.toString('hex').substring(0, 100)
    };
    
    sflowData.push(flowRecord);
    
    // 保持最近1000条记录
    if (sflowData.length > 1000) {
        sflowData.splice(0, sflowData.length - 1000);
    }
    
    console.log(\`收到 sFlow 数据: \${rinfo.address}:\${rinfo.port} -> \${msg.length} 字节\`);
});

sflowServer.bind(6343, () => {
    console.log('sFlow 收集器已启动，监听端口 6343');
});

// Web 服务器
const webServer = http.createServer((req, res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Content-Type', 'application/json');
    
    if (req.url === '/api/sflow') {
        res.writeHead(200);
        res.end(JSON.stringify({
            status: 'running',
            dataPoints: sflowData.length,
            latestData: sflowData.slice(-10),
            timestamp: new Date().toISOString()
        }));
    } else if (req.url === '/') {
        res.setHeader('Content-Type', 'text/html');
        res.writeHead(200);
        res.end(\`
<!DOCTYPE html>
<html>
<head>
    <title>sFlow-RT 模拟器</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; }
        .stats { background: #ecf0f1; padding: 15px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>sFlow-RT 模拟器</h1>
        <p>本地 sFlow 数据收集服务</p>
    </div>
    <div class="stats">
        <h2>服务状态</h2>
        <p>✅ sFlow 收集器运行中 (端口 6343)</p>
        <p>✅ Web API 可用 (端口 8008)</p>
        <p>📊 已收集数据点: \${sflowData.length}</p>
    </div>
    <div class="stats">
        <h2>API 端点</h2>
        <p><a href="/api/sflow">/api/sflow</a> - 获取 sFlow 数据</p>
    </div>
</body>
</html>
        \`);
    } else {
        res.writeHead(404);
        res.end('Not Found');
    }
});

webServer.listen(8008, () => {
    console.log('Web 服务器已启动，监听端口 8008');
});

// 模拟数据生成器
setInterval(() => {
    const mockData = {
        timestamp: new Date().toISOString(),
        source: '192.168.1.' + Math.floor(Math.random() * 254 + 1),
        port: Math.floor(Math.random() * 65535),
        size: Math.floor(Math.random() * 1500),
        data: 'mock_sflow_data_' + Math.random().toString(36).substring(7)
    };
    
    sflowData.push(mockData);
    
    if (sflowData.length > 1000) {
        sflowData.splice(0, sflowData.length - 1000);
    }
}, 5000);
EOF

# 启动脚本
COPY <<EOF /app/start.sh
#!/bin/bash
echo "启动 sFlow 模拟器..."
node /app/sflow-simulator.js
EOF

RUN chmod +x /app/start.sh

EXPOSE 8008 6343/udp

CMD ["/app/start.sh"]
