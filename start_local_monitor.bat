@echo off
chcp 65001 >nul
echo ========================================
echo    网络流量监控系统 - 本地版本
echo ========================================
echo.
echo 正在启动本地监控服务...
echo.

REM 检查 Python 是否可用
python --version >nul 2>&1
if not errorlevel 1 (
    echo Python 可用，启动 Python 监控服务...
    python local_monitor.py
    goto :end
)

REM 检查 Node.js 是否可用
node --version >nul 2>&1
if not errorlevel 1 (
    echo Node.js 可用，但需要创建 Node.js 脚本...
    echo 当前使用静态页面展示
    goto :static
)

:static
echo 启动静态页面展示...
echo.
echo 服务信息:
echo - 状态页面: index.html
echo - 配置文件: docker-compose.yml
echo - 部署文档: DEPLOYMENT_REPORT.md
echo.
echo 由于网络连接问题，Docker 部署失败
echo 已创建完整的配置文件和文档
echo 网络问题解决后可立即重新部署
echo.

REM 尝试打开状态页面
if exist "index.html" (
    echo 正在打开状态页面...
    start "" "index.html"
) else (
    echo 状态页面不存在，请检查文件
)

echo.
echo 可用的解决方案:
echo 1. 解决网络问题后运行: docker-compose up -d
echo 2. 手动安装 InfluxDB、Grafana、sFlow-RT
echo 3. 使用云服务替代本地部署
echo.

:end
pause
