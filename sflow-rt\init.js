// sFlow-RT initialization script

// Load required modules
var influxdb = 'http://influxdb:8086';
var dbname = 'netmon';
var measurement = 'sflow';
var auth = 'admin:admin123';

// Set up InfluxDB output
setFlow('influxdb', {
  keys: 'ipsource,ipdestination,ipsourcegroup,ipdestinationgroup,ipprotocol,ipsourcetag,ipdestinationtag',
  value: 'bytes',
  log: true,
  flowStart: true
});

// Function to get IP geolocation
function getGeoLocation(ip) {
  try {
    // This will be replaced with ip2region integration
    // For now, return a dummy location
    return {
      country: 'CN',
      region: 'Beijing',
      city: 'Beijing',
      isp: 'Unknown'
    };
  } catch (e) {
    logWarning('Error getting geolocation: ' + e);
    return null;
  }
}

// Process flow records
setFlowHandler(function(rec) {
  var source = rec.flowKeys.split(',')[0];
  var destination = rec.flowKeys.split(',')[1];
  
  // Get geolocation for source and destination
  var srcGeo = getGeoLocation(source);
  var dstGeo = getGeoLocation(destination);
  
  // Prepare data for InfluxDB
  var data = [
    {
      measurement: measurement,
      tags: {
        source: source,
        destination: destination,
        protocol: rec.flowKeys.split(',')[4],
        source_country: srcGeo ? srcGeo.country : 'Unknown',
        source_region: srcGeo ? srcGeo.region : 'Unknown',
        source_city: srcGeo ? srcGeo.city : 'Unknown',
        destination_country: dstGeo ? dstGeo.country : 'Unknown',
        destination_region: dstGeo ? dstGeo.region : 'Unknown',
        destination_city: dstGeo ? dstGeo.city : 'Unknown'
      },
      fields: {
        bytes: rec.value,
        packets: rec.packets,
        duration: rec.duration
      },
      timestamp: new Date(rec.endTime).getTime() * 1000000 // Convert to nanoseconds
    }
  ];

  // Send data to InfluxDB
  http2({
    url: influxdb + '/api/v2/write?org=netmon&bucket=' + dbname + '&precision=ns',
    method: 'POST',
    headers: {
      'Authorization': 'Token ' + auth,
      'Content-Type': 'text/plain; charset=utf-8',
      'Accept': 'application/json'
    },
    body: data.map(point => {
      return point.measurement + 
        ',source=' + point.tags.source +
        ',destination=' + point.tags.destination +
        ',protocol=' + point.tags.protocol +
        ',source_country=' + point.tags.source_country +
        ',source_region=' + point.tags.source_region +
        ',source_city=' + point.tags.source_city +
        ',destination_country=' + point.tags.destination_country +
        ',destination_region=' + point.tags.destination_region +
        ',destination_city=' + point.tags.destination_city +
        ' bytes=' + point.fields.bytes +
        ',packets=' + point.fields.packets +
        ',duration=' + point.fields.duration +
        ' ' + point.timestamp;
    }).join('\n')
  });
}, ['influxdb']);

// Start the flow monitoring
setFlow('all', {value:'bytes',t:20});
