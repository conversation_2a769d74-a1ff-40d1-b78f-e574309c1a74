# 可视化服务 Dockerfile
FROM alpine:latest

# 安装必要的包
RUN apk add --no-cache \
    nodejs \
    npm \
    curl \
    bash

# 创建应用目录
WORKDIR /app

# 创建可视化服务
COPY <<EOF /app/viz-service.js
const http = require('http');
const fs = require('fs');
const path = require('path');

// 可视化服务
const server = http.createServer((req, res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    const url = new URL(req.url, \`http://\${req.headers.host}\`);
    
    if (req.method === 'GET' && url.pathname === '/api/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
            database: 'ok',
            version: '1.0.0',
            timestamp: new Date().toISOString()
        }));
    }
    else if (req.method === 'GET' && url.pathname === '/login') {
        res.setHeader('Content-Type', 'text/html');
        res.writeHead(200);
        res.end(\`
<!DOCTYPE html>
<html>
<head>
    <title>网络监控系统 - 登录</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 50px; }
        .login-box { max-width: 400px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .login-box h1 { text-align: center; color: #2c3e50; margin-bottom: 30px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; color: #555; }
        .form-group input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { width: 100%; padding: 12px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #2980b9; }
    </style>
</head>
<body>
    <div class="login-box">
        <h1>🌐 网络监控系统</h1>
        <form action="/" method="get">
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" value="admin" readonly>
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" value="admin" readonly>
            </div>
            <button type="submit" class="btn">登录</button>
        </form>
        <p style="text-align: center; margin-top: 20px; color: #666;">
            默认凭据: admin/admin
        </p>
    </div>
</body>
</html>
        \`);
    }
    else if (req.method === 'GET' && url.pathname === '/') {
        res.setHeader('Content-Type', 'text/html');
        res.writeHead(200);
        res.end(\`
<!DOCTYPE html>
<html>
<head>
    <title>网络监控仪表板</title>
    <meta charset="utf-8">
    <meta http-equiv="refresh" content="30">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 20px; text-align: center; }
        .container { max-width: 1200px; margin: 20px auto; padding: 0 20px; }
        .dashboard-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .panel { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .panel h3 { color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        .metric { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #eee; }
        .metric:last-child { border-bottom: none; }
        .metric-value { font-weight: bold; color: #27ae60; }
        .chart-placeholder { height: 200px; background: #ecf0f1; border-radius: 5px; display: flex; align-items: center; justify-content: center; color: #666; }
        .status-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-right: 8px; }
        .status-online { background: #27ae60; }
        .status-warning { background: #f39c12; }
        .footer { text-align: center; padding: 20px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌐 网络流量监控仪表板</h1>
        <p><span class="status-indicator status-online"></span>系统运行正常 | 最后更新: \${new Date().toLocaleString('zh-CN')}</p>
    </div>
    
    <div class="container">
        <div class="dashboard-grid">
            <div class="panel">
                <h3>📊 实时流量统计</h3>
                <div class="metric">
                    <span>入站流量:</span>
                    <span class="metric-value">\${Math.floor(Math.random() * 1000)} MB/s</span>
                </div>
                <div class="metric">
                    <span>出站流量:</span>
                    <span class="metric-value">\${Math.floor(Math.random() * 500)} MB/s</span>
                </div>
                <div class="metric">
                    <span>总连接数:</span>
                    <span class="metric-value">\${Math.floor(Math.random() * 1000 + 500)}</span>
                </div>
                <div class="metric">
                    <span>活动会话:</span>
                    <span class="metric-value">\${Math.floor(Math.random() * 200 + 100)}</span>
                </div>
            </div>
            
            <div class="panel">
                <h3>🔗 网络连接状态</h3>
                <div class="metric">
                    <span><span class="status-indicator status-online"></span>已建立连接:</span>
                    <span class="metric-value">\${Math.floor(Math.random() * 800 + 200)}</span>
                </div>
                <div class="metric">
                    <span><span class="status-indicator status-warning"></span>等待连接:</span>
                    <span class="metric-value">\${Math.floor(Math.random() * 50)}</span>
                </div>
                <div class="metric">
                    <span><span class="status-indicator status-online"></span>监听端口:</span>
                    <span class="metric-value">\${Math.floor(Math.random() * 20 + 10)}</span>
                </div>
            </div>
            
            <div class="panel">
                <h3>📈 流量趋势图</h3>
                <div class="chart-placeholder">
                    📈 实时流量图表<br>
                    (模拟数据显示)
                </div>
            </div>
            
            <div class="panel">
                <h3>🌍 地理位置分布</h3>
                <div class="chart-placeholder">
                    🗺️ IP 地理位置分布图<br>
                    (基于 sFlow 数据)
                </div>
            </div>
            
            <div class="panel">
                <h3>⚠️ 系统告警</h3>
                <div class="metric">
                    <span>高流量告警:</span>
                    <span class="metric-value">0</span>
                </div>
                <div class="metric">
                    <span>连接异常:</span>
                    <span class="metric-value">0</span>
                </div>
                <div class="metric">
                    <span>系统状态:</span>
                    <span class="metric-value">正常</span>
                </div>
            </div>
            
            <div class="panel">
                <h3>🔧 服务状态</h3>
                <div class="metric">
                    <span><span class="status-indicator status-online"></span>sFlow-RT:</span>
                    <span class="metric-value">运行中</span>
                </div>
                <div class="metric">
                    <span><span class="status-indicator status-online"></span>数据存储:</span>
                    <span class="metric-value">运行中</span>
                </div>
                <div class="metric">
                    <span><span class="status-indicator status-online"></span>可视化服务:</span>
                    <span class="metric-value">运行中</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>🌐 网络流量监控系统 | 基于 sFlow-RT + 数据存储 + 可视化服务</p>
        <p>访问地址: <a href="http://localhost:3000">http://localhost:3000</a> | 
           sFlow-RT: <a href="http://localhost:8008">http://localhost:8008</a> | 
           数据存储: <a href="http://localhost:8086">http://localhost:8086</a></p>
    </div>
    
    <script>
        // 自动刷新页面
        setTimeout(() => location.reload(), 30000);
    </script>
</body>
</html>
        \`);
    }
    else {
        res.writeHead(404);
        res.end('Not Found');
    }
});

server.listen(3000, () => {
    console.log('可视化服务已启动，监听端口 3000');
});
EOF

# 启动脚本
COPY <<EOF /app/start.sh
#!/bin/bash
echo "启动可视化服务..."
node /app/viz-service.js
EOF

RUN chmod +x /app/start.sh

EXPOSE 3000

CMD ["/app/start.sh"]
