# 工作版本的网络监控系统
param([int]$Port = 8008)

Write-Host "启动网络监控系统..." -ForegroundColor Green
Write-Host "端口: $Port" -ForegroundColor Yellow

# 创建 HTML 内容函数
function Get-DashboardHTML {
    $networkStats = Get-NetAdapterStatistics | Where-Object { $_.Name -notlike "*Loopback*" }
    $connections = Get-NetTCPConnection | Where-Object { $_.State -eq "Established" }
    $totalBytes = ($networkStats | Measure-Object -Property BytesReceived, BytesSent -Sum).Sum
    
    return @"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>网络监控系统</title>
    <meta http-equiv="refresh" content="10">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; text-align: center; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-box { background: white; padding: 15px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .stat-value { font-size: 1.5em; font-weight: bold; color: #3498db; }
        .stat-label { color: #666; margin-top: 5px; }
        .status { background: white; padding: 20px; border-radius: 5px; margin: 10px 0; }
        .success { color: #27ae60; }
        .info { background: #ecf0f1; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>网络流量监控系统</h1>
        <p>本地 PowerShell 监控服务</p>
        <p>当前时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')</p>
    </div>
    
    <div class="stats">
        <div class="stat-box">
            <div class="stat-value">$([math]::Round($totalBytes / 1MB, 2))</div>
            <div class="stat-label">总流量 (MB)</div>
        </div>
        <div class="stat-box">
            <div class="stat-value">$($connections.Count)</div>
            <div class="stat-label">活动连接</div>
        </div>
        <div class="stat-box">
            <div class="stat-value">$($networkStats.Count)</div>
            <div class="stat-label">网络适配器</div>
        </div>
        <div class="stat-box">
            <div class="stat-value">$(($connections | Select-Object -ExpandProperty RemoteAddress -Unique).Count)</div>
            <div class="stat-label">唯一远程IP</div>
        </div>
    </div>
    
    <div class="status">
        <h2>系统状态</h2>
        <p class="success">✅ 监控服务运行中</p>
        <p class="success">✅ Web 界面可访问 (端口 $Port)</p>
        <p class="success">✅ 网络统计收集正常</p>
    </div>
    
    <div class="status">
        <h2>网络适配器详情</h2>
        $(foreach ($adapter in $networkStats) {
            "<div class='info'>"
            "<strong>$($adapter.Name)</strong><br>"
            "接收: $([math]::Round($adapter.BytesReceived / 1MB, 2)) MB | "
            "发送: $([math]::Round($adapter.BytesSent / 1MB, 2)) MB<br>"
            "数据包: 接收 $($adapter.PacketsReceived) | 发送 $($adapter.PacketsSent)"
            "</div>"
        })
    </div>
    
    <div class="status">
        <h2>活动连接 (前10个)</h2>
        $(
            $topConnections = $connections | Group-Object RemoteAddress | Sort-Object Count -Descending | Select-Object -First 10
            foreach ($conn in $topConnections) {
                "<div class='info'>$($conn.Name) - $($conn.Count) 个连接</div>"
            }
        )
    </div>
    
    <div class="status">
        <h2>部署状态说明</h2>
        <div class="info">
            <p><strong>原计划:</strong> 使用 Docker 部署 sFlow-RT + InfluxDB + Grafana</p>
            <p><strong>当前状态:</strong> 由于网络连接限制，使用 PowerShell 本地监控</p>
            <p><strong>功能对比:</strong></p>
            <ul>
                <li>✅ 实时网络统计监控</li>
                <li>✅ 连接状态分析</li>
                <li>✅ Web 界面展示</li>
                <li>❌ sFlow 协议解析 (需要网络设备配置)</li>
                <li>❌ 历史数据存储 (InfluxDB)</li>
                <li>❌ 高级可视化 (Grafana)</li>
            </ul>
        </div>
    </div>
    
    <div style="text-align: center; margin-top: 20px; color: #666;">
        <p>页面每 10 秒自动刷新</p>
        <p>按 Ctrl+C 在 PowerShell 窗口中停止服务</p>
    </div>
</body>
</html>
"@
}

# 启动 HTTP 服务器
try {
    $listener = New-Object System.Net.HttpListener
    $listener.Prefixes.Add("http://localhost:$Port/")
    $listener.Start()
    
    Write-Host "✅ 服务已启动: http://localhost:$Port" -ForegroundColor Green
    Write-Host "🔄 提供实时网络监控数据" -ForegroundColor Cyan
    Write-Host "🛑 按 Ctrl+C 停止服务" -ForegroundColor Red
    Write-Host ""
    
    while ($true) {
        $context = $listener.GetContext()
        $response = $context.Response
        
        $html = Get-DashboardHTML
        $buffer = [System.Text.Encoding]::UTF8.GetBytes($html)
        
        $response.ContentLength64 = $buffer.Length
        $response.ContentType = "text/html; charset=utf-8"
        $response.OutputStream.Write($buffer, 0, $buffer.Length)
        $response.OutputStream.Close()
        
        Write-Host "$(Get-Date -Format 'HH:mm:ss') - 页面请求已处理" -ForegroundColor Gray
    }
}
catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}
finally {
    if ($listener) {
        $listener.Stop()
        Write-Host "服务已停止" -ForegroundColor Yellow
    }
}
