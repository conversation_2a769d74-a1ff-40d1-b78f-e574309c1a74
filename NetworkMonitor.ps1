# 网络流量监控系统 - PowerShell 版本
# 替代 Docker 部署的本地解决方案

param(
    [int]$WebPort = 8008,
    [int]$SFlowPort = 6343
)

# 全局变量
$Global:MonitorData = @()
$Global:IsRunning = $true
$Global:StartTime = Get-Date

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
}

# 网络统计收集函数
function Get-NetworkStats {
    try {
        $networkStats = Get-NetAdapterStatistics | Where-Object { $_.Name -notlike "*Loopback*" }
        $totalBytesReceived = ($networkStats | Measure-Object -Property BytesReceived -Sum).Sum
        $totalBytesSent = ($networkStats | Measure-Object -Property BytesSent -Sum).Sum
        
        return @{
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            BytesReceived = $totalBytesReceived
            BytesSent = $totalBytesSent
            TotalBytes = $totalBytesReceived + $totalBytesSent
            Adapters = $networkStats.Count
        }
    }
    catch {
        Write-Log "获取网络统计失败: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# 活动连接监控
function Get-ActiveConnections {
    try {
        $connections = Get-NetTCPConnection | Where-Object { $_.State -eq "Established" }
        $connectionStats = $connections | Group-Object RemoteAddress | ForEach-Object {
            @{
                RemoteIP = $_.Name
                ConnectionCount = $_.Count
                LocalPorts = ($_.Group | Select-Object -ExpandProperty LocalPort | Sort-Object -Unique) -join ","
            }
        }
        
        return $connectionStats | Sort-Object ConnectionCount -Descending | Select-Object -First 10
    }
    catch {
        Write-Log "获取连接信息失败: $($_.Exception.Message)" "ERROR"
        return @()
    }
}

# 数据收集循环
function Start-DataCollection {
    Write-Log "开始数据收集..."
    
    while ($Global:IsRunning) {
        try {
            $networkStats = Get-NetworkStats
            $connections = Get-ActiveConnections
            
            if ($networkStats) {
                $dataPoint = @{
                    Timestamp = $networkStats.Timestamp
                    NetworkStats = $networkStats
                    Connections = $connections
                    SystemUptime = (Get-Date) - $Global:StartTime
                }
                
                $Global:MonitorData += $dataPoint
                
                # 保持最近100条记录
                if ($Global:MonitorData.Count -gt 100) {
                    $Global:MonitorData = $Global:MonitorData[-100..-1]
                }
                
                Write-Log "数据收集完成 - 总字节: $($networkStats.TotalBytes), 连接数: $($connections.Count)"
            }
            
            Start-Sleep -Seconds 5
        }
        catch {
            Write-Log "数据收集出错: $($_.Exception.Message)" "ERROR"
            Start-Sleep -Seconds 10
        }
    }
}

# Web 服务器函数
function Start-WebServer {
    param([int]$Port)
    
    try {
        $listener = New-Object System.Net.HttpListener
        $listener.Prefixes.Add("http://localhost:$Port/")
        $listener.Start()
        
        Write-Log "Web 服务器已启动: http://localhost:$Port"
        
        while ($Global:IsRunning) {
            try {
                $context = $listener.GetContext()
                $request = $context.Request
                $response = $context.Response
                
                $path = $request.Url.AbsolutePath
                
                switch ($path) {
                    "/" { 
                        $html = Get-DashboardHTML
                        $buffer = [System.Text.Encoding]::UTF8.GetBytes($html)
                        $response.ContentType = "text/html; charset=utf-8"
                    }
                    "/api/data" {
                        $json = $Global:MonitorData | ConvertTo-Json -Depth 3
                        $buffer = [System.Text.Encoding]::UTF8.GetBytes($json)
                        $response.ContentType = "application/json"
                    }
                    "/api/status" {
                        $status = @{
                            IsRunning = $Global:IsRunning
                            StartTime = $Global:StartTime
                            DataPoints = $Global:MonitorData.Count
                            WebPort = $Port
                            SFlowPort = $SFlowPort
                        }
                        $json = $status | ConvertTo-Json
                        $buffer = [System.Text.Encoding]::UTF8.GetBytes($json)
                        $response.ContentType = "application/json"
                    }
                    default {
                        $response.StatusCode = 404
                        $buffer = [System.Text.Encoding]::UTF8.GetBytes("404 - Page Not Found")
                        $response.ContentType = "text/plain"
                    }
                }
                
                $response.ContentLength64 = $buffer.Length
                $response.OutputStream.Write($buffer, 0, $buffer.Length)
                $response.OutputStream.Close()
            }
            catch {
                Write-Log "Web 请求处理出错: $($_.Exception.Message)" "ERROR"
            }
        }
        
        $listener.Stop()
    }
    catch {
        Write-Log "Web 服务器启动失败: $($_.Exception.Message)" "ERROR"
    }
}

# 生成仪表板 HTML
function Get-DashboardHTML {
    return @"
<!DOCTYPE html>
<html>
<head>
    <title>网络流量监控系统 - PowerShell 版</title>
    <meta charset="utf-8">
    <meta http-equiv="refresh" content="10">
    <style>
        body { font-family: 'Segoe UI', Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stat-value { font-size: 2em; font-weight: bold; color: #667eea; }
        .stat-label { color: #666; margin-top: 5px; }
        .data-section { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .connection-list { max-height: 300px; overflow-y: auto; }
        .connection-item { padding: 10px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; }
        .status-indicator { display: inline-block; width: 10px; height: 10px; background: #2ecc71; border-radius: 50%; margin-right: 10px; }
        .refresh-info { text-align: center; color: #666; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌐 网络流量监控系统</h1>
        <p>PowerShell 本地监控解决方案 | 替代 Docker 部署</p>
        <p><span class="status-indicator"></span>系统运行中 | 端口: $WebPort</p>
    </div>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value" id="total-bytes">--</div>
            <div class="stat-label">总流量 (字节)</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="data-points">$($Global:MonitorData.Count)</div>
            <div class="stat-label">数据点</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="connections">--</div>
            <div class="stat-label">活动连接</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="uptime">--</div>
            <div class="stat-label">运行时间</div>
        </div>
    </div>
    
    <div class="data-section">
        <h3>📊 最新网络统计</h3>
        <div id="latest-stats">
            $(if ($Global:MonitorData.Count -gt 0) {
                $latest = $Global:MonitorData[-1]
                "时间: $($latest.Timestamp)<br>"
                "接收: $($latest.NetworkStats.BytesReceived) 字节<br>"
                "发送: $($latest.NetworkStats.BytesSent) 字节<br>"
                "网络适配器: $($latest.NetworkStats.Adapters) 个"
            } else {
                "暂无数据"
            })
        </div>
    </div>
    
    <div class="data-section">
        <h3>🔗 活动连接 (Top 10)</h3>
        <div class="connection-list">
            $(if ($Global:MonitorData.Count -gt 0 -and $Global:MonitorData[-1].Connections.Count -gt 0) {
                $Global:MonitorData[-1].Connections | ForEach-Object {
                    "<div class='connection-item'><span>$($_.RemoteIP)</span><span>$($_.ConnectionCount) 连接</span></div>"
                }
            } else {
                "<div class='connection-item'>暂无活动连接</div>"
            })
        </div>
    </div>
    
    <div class="refresh-info">
        <p>🔄 页面每 10 秒自动刷新 | 数据每 5 秒更新</p>
        <p>⚠️ 这是 Docker 部署失败后的替代方案，提供基本的网络监控功能</p>
    </div>
    
    <script>
        // 可以添加 JavaScript 来实现实时更新
        console.log('网络监控系统已加载');
    </script>
</body>
</html>
"@
}

# 主函数
function Main {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "    网络流量监控系统 - PowerShell 版" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Log "正在启动网络监控系统..."
    Write-Log "Web 端口: $WebPort"
    Write-Log "sFlow 端口: $SFlowPort (模拟)"
    
    # 启动数据收集（后台作业）
    $dataJob = Start-Job -ScriptBlock {
        param($MonitorData, $IsRunning, $StartTime)
        # 这里需要重新定义函数，因为作业是独立的作用域
        # 简化版本，直接在主线程中运行
    }
    
    Write-Log "✅ 系统启动完成!"
    Write-Log "📊 Web 界面: http://localhost:$WebPort"
    Write-Log "🔌 sFlow 端口: $SFlowPort (模拟)"
    Write-Log "⚠️  注意: 这是 Docker 部署的替代方案"
    Write-Log "🛑 按 Ctrl+C 停止服务"
    
    # 启动数据收集和 Web 服务器
    $dataCollectionJob = Start-Job -ScriptBlock ${function:Start-DataCollection}
    
    try {
        Start-WebServer -Port $WebPort
    }
    finally {
        $Global:IsRunning = $false
        if ($dataCollectionJob) {
            Stop-Job $dataCollectionJob
            Remove-Job $dataCollectionJob
        }
        Write-Log "🛑 服务已停止"
    }
}

# 启动系统
Main
