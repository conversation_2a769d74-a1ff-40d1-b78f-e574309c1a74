# Docker Compose 文件 - 网络流量监控系统
# 移除过时的 version 属性，使用现代 Compose 文件格式
# https://docs.docker.com/compose/compose-file/

services:
  # sFlow-RT for real-time sFlow data processing
  sflow-rt:
    image: sflow/sflow-rt
    container_name: sflow-rt
    hostname: sflow-rt
    ports:
      - "8008:8008"  # Web UI
      - "6343:6343/udp"  # sFlow
      - "8087:8087"  # REST API
    volumes:
      - ./sflow-rt/init.js:/sflow-rt/init.js
      - ./sflow-rt/db:/sflow-rt/db
    environment:
      - RT_IP=0.0.0.0
      - RT_PORT=8008
    restart: unless-stopped
    networks:
      - netmon

  # InfluxDB for time-series data storage
  influxdb:
    image: influxdb:2.7
    container_name: influxdb
    hostname: influxdb
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=admin123
      - DOCKER_INFLUXDB_INIT_ORG=netmon
      - DOCKER_INFLUXDB_INIT_BUCKET=netmon
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=my-super-secret-auth-token
    restart: unless-stopped
    networks:
      - netmon

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    hostname: grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: unless-stopped
    depends_on:
      - influxdb
    networks:
      - netmon

  # Elasticsearch for log analysis (optional) - commented out for initial deployment
  # elasticsearch:
  #   image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
  #   container_name: elasticsearch
  #   environment:
  #     - discovery.type=single-node
  #     - xpack.security.enabled=false
  #     - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
  #   ulimits:
  #     memlock:
  #       soft: -1
  #       hard: -1
  #   volumes:
  #     - elasticsearch_data:/usr/share/elasticsearch/data
  #   ports:
  #     - "9200:9200"
  #   networks:
  #     - netmon

  # Kibana for Elasticsearch visualization (optional) - commented out for initial deployment
  # kibana:
  #   image: docker.elastic.co/kibana/kibana:8.9.0
  #   container_name: kibana
  #   ports:
  #     - "5601:5601"
  #   environment:
  #     - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
  #   depends_on:
  #     - elasticsearch
  #   networks:
  #     - netmon

networks:
  netmon:
    driver: bridge

volumes:
  influxdb_data:
  grafana_data:
  elasticsearch_data:
