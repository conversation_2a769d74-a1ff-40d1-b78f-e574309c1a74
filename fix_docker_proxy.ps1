# Docker 代理配置修复脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Docker 代理配置修复工具" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "步骤 1: 检查当前 Docker 代理配置..." -ForegroundColor Yellow
docker --context desktop-linux system info | Select-String -Pattern "Proxy"

Write-Host ""
Write-Host "步骤 2: 尝试重启 Docker Desktop 以清除代理配置..." -ForegroundColor Yellow

# 停止 Docker Desktop
Write-Host "正在停止 Docker Desktop..." -ForegroundColor Cyan
Stop-Process -Name "Docker Desktop" -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 5

# 清除可能的代理环境变量
Write-Host "清除代理环境变量..." -ForegroundColor Cyan
$env:HTTP_PROXY = ""
$env:HTTPS_PROXY = ""
$env:http_proxy = ""
$env:https_proxy = ""

# 重新启动 Docker Desktop
Write-Host "重新启动 Docker Desktop..." -ForegroundColor Cyan
Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe" -WindowStyle Hidden

Write-Host "等待 Docker Desktop 启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

Write-Host ""
Write-Host "步骤 3: 测试 Docker 连接..." -ForegroundColor Yellow

# 测试 Docker 连接
$maxRetries = 5
$retryCount = 0
$success = $false

while ($retryCount -lt $maxRetries -and -not $success) {
    $retryCount++
    Write-Host "尝试 $retryCount/$maxRetries : 测试 Docker 连接..." -ForegroundColor Cyan
    
    try {
        $result = docker --context desktop-linux info 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker 连接成功!" -ForegroundColor Green
            $success = $true
        } else {
            Write-Host "❌ Docker 连接失败，等待重试..." -ForegroundColor Red
            Start-Sleep -Seconds 10
        }
    }
    catch {
        Write-Host "❌ Docker 连接异常，等待重试..." -ForegroundColor Red
        Start-Sleep -Seconds 10
    }
}

if ($success) {
    Write-Host ""
    Write-Host "步骤 4: 测试镜像拉取..." -ForegroundColor Yellow
    
    try {
        Write-Host "尝试拉取 hello-world 镜像..." -ForegroundColor Cyan
        $pullResult = docker --context desktop-linux pull hello-world 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 镜像拉取成功！Docker 网络问题已解决。" -ForegroundColor Green
            Write-Host ""
            Write-Host "现在可以部署完整的监控系统了！" -ForegroundColor Green
            Write-Host "运行命令: docker --context desktop-linux compose up -d" -ForegroundColor Cyan
        } else {
            Write-Host "❌ 镜像拉取失败: $pullResult" -ForegroundColor Red
            Write-Host ""
            Write-Host "建议手动配置 Docker Desktop:" -ForegroundColor Yellow
            Write-Host "1. 打开 Docker Desktop" -ForegroundColor White
            Write-Host "2. 进入 Settings -> Resources -> Proxies" -ForegroundColor White
            Write-Host "3. 禁用所有代理设置" -ForegroundColor White
            Write-Host "4. 进入 Settings -> Docker Engine" -ForegroundColor White
            Write-Host "5. 添加镜像源配置" -ForegroundColor White
        }
    }
    catch {
        Write-Host "❌ 镜像拉取测试失败" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Docker 启动失败，请手动检查 Docker Desktop" -ForegroundColor Red
}

Write-Host ""
Write-Host "脚本执行完成。" -ForegroundColor Gray
