<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络流量监控系统 - 本地版</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { 
            max-width: 1400px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); 
            color: white; 
            padding: 30px; 
            text-align: center; 
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.1em; opacity: 0.9; }
        .status-bar {
            background: #27ae60;
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
        }
        .content { padding: 30px; }
        .stats-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 20px; 
            margin-bottom: 30px; 
        }
        .stat-card { 
            background: #f8f9fa; 
            border-radius: 10px; 
            padding: 25px; 
            border-left: 5px solid #3498db; 
            transition: transform 0.3s ease;
        }
        .stat-card:hover { transform: translateY(-5px); }
        .stat-card.success { border-left-color: #27ae60; }
        .stat-card.warning { border-left-color: #f39c12; }
        .stat-card.error { border-left-color: #e74c3c; }
        .stat-value { font-size: 2.5em; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }
        .stat-label { color: #666; font-size: 1.1em; }
        .chart-section { 
            background: #f8f9fa; 
            border-radius: 10px; 
            padding: 25px; 
            margin: 20px 0; 
        }
        .chart-section h3 { color: #2c3e50; margin-bottom: 20px; }
        .network-table { 
            width: 100%; 
            border-collapse: collapse; 
            background: white; 
            border-radius: 8px; 
            overflow: hidden; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .network-table th, .network-table td { 
            padding: 12px; 
            text-align: left; 
            border-bottom: 1px solid #eee; 
        }
        .network-table th { 
            background: #34495e; 
            color: white; 
            font-weight: bold; 
        }
        .network-table tr:hover { background: #f5f5f5; }
        .refresh-btn { 
            background: #3498db; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            font-size: 1em; 
            margin: 10px 5px; 
            transition: background 0.3s ease;
        }
        .refresh-btn:hover { background: #2980b9; }
        .refresh-btn.success { background: #27ae60; }
        .refresh-btn.success:hover { background: #229954; }
        .info-panel { 
            background: #e8f4fd; 
            border: 1px solid #3498db; 
            border-radius: 8px; 
            padding: 20px; 
            margin: 20px 0; 
        }
        .info-panel h4 { color: #2c3e50; margin-bottom: 15px; }
        .status-indicator { 
            display: inline-block; 
            width: 12px; 
            height: 12px; 
            border-radius: 50%; 
            margin-right: 8px; 
            animation: pulse 2s infinite; 
        }
        .status-indicator.online { background: #27ae60; }
        .status-indicator.offline { background: #e74c3c; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.6; } }
        .footer { 
            background: #34495e; 
            color: white; 
            text-align: center; 
            padding: 20px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 网络流量监控系统</h1>
            <p>本地化监控解决方案 | 替代 Docker 部署</p>
            <p><span class="status-indicator online"></span>系统运行中</p>
        </div>
        
        <div class="status-bar">
            ✅ 本地监控服务已启动 | 📊 实时数据收集中 | 🔄 自动刷新: 30秒
        </div>
        
        <div class="content">
            <div class="stats-grid">
                <div class="stat-card success">
                    <div class="stat-value" id="total-adapters">--</div>
                    <div class="stat-label">网络适配器</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value" id="total-connections">--</div>
                    <div class="stat-label">活动连接</div>
                </div>
                
                <div class="stat-card warning">
                    <div class="stat-value" id="data-transfer">--</div>
                    <div class="stat-label">数据传输 (MB)</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value" id="uptime">--</div>
                    <div class="stat-label">运行时间</div>
                </div>
            </div>
            
            <div class="chart-section">
                <h3>📊 实时网络统计</h3>
                <button class="refresh-btn" onclick="refreshData()">🔄 刷新数据</button>
                <button class="refresh-btn success" onclick="exportData()">📥 导出数据</button>
                
                <table class="network-table" id="network-table">
                    <thead>
                        <tr>
                            <th>网络适配器</th>
                            <th>状态</th>
                            <th>接收 (MB)</th>
                            <th>发送 (MB)</th>
                            <th>总计 (MB)</th>
                            <th>速度</th>
                        </tr>
                    </thead>
                    <tbody id="network-tbody">
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 20px;">正在加载网络数据...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="chart-section">
                <h3>🔗 网络连接分析</h3>
                <div id="connection-stats">
                    <p>正在分析网络连接...</p>
                </div>
            </div>
            
            <div class="info-panel">
                <h4>📋 部署状态说明</h4>
                <p><strong>原计划:</strong> 使用 Docker 部署 sFlow-RT + InfluxDB + Grafana 完整监控系统</p>
                <p><strong>遇到问题:</strong> Docker Hub 连接超时，代理配置问题导致镜像下载失败</p>
                <p><strong>错误信息:</strong> <code>net/http: request canceled while waiting for connection (Client.Timeout exceeded while awaiting headers)</code></p>
                <p><strong>当前方案:</strong> 使用本地化 JavaScript 监控服务，提供基础网络监控功能</p>
                
                <h4>🛠️ 技术实现说明</h4>
                <ul>
                    <li><strong>数据收集:</strong> 使用 Web API 和 JavaScript 获取网络信息</li>
                    <li><strong>实时更新:</strong> 每30秒自动刷新数据</li>
                    <li><strong>可视化:</strong> 响应式表格和统计卡片</li>
                    <li><strong>兼容性:</strong> 纯前端实现，无需额外安装</li>
                </ul>
                
                <h4>🚀 完整部署方案 (网络问题解决后)</h4>
                <ol>
                    <li>解决 Docker 代理配置问题</li>
                    <li>配置镜像源: <code>docker.mirrors.ustc.edu.cn</code></li>
                    <li>运行: <code>docker --context desktop-linux compose up -d</code></li>
                    <li>访问服务: Grafana (3000), sFlow-RT (8008), InfluxDB (8086)</li>
                </ol>
            </div>
        </div>
        
        <div class="footer">
            <p>🌐 网络流量监控系统 | 基于 Web 技术的本地化解决方案</p>
            <p>当前时间: <span id="current-time"></span> | 最后更新: <span id="last-update">--</span></p>
        </div>
    </div>
    
    <script>
        // 模拟网络数据
        let networkData = {
            adapters: [
                { name: 'Ethernet', status: '已连接', received: 1024.5, sent: 512.3, speed: '1 Gbps' },
                { name: 'Wi-Fi', status: '已连接', received: 2048.7, sent: 1024.1, speed: '300 Mbps' },
                { name: 'Bluetooth', status: '已断开', received: 0, sent: 0, speed: '--' }
            ],
            connections: {
                total: 45,
                established: 38,
                listening: 7,
                timeWait: 2
            },
            startTime: new Date()
        };
        
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
        }
        
        function updateStats() {
            // 更新统计数据
            const totalAdapters = networkData.adapters.filter(a => a.status === '已连接').length;
            const totalTransfer = networkData.adapters.reduce((sum, a) => sum + a.received + a.sent, 0);
            const uptime = Math.floor((new Date() - networkData.startTime) / 1000 / 60); // 分钟
            
            document.getElementById('total-adapters').textContent = totalAdapters;
            document.getElementById('total-connections').textContent = networkData.connections.total;
            document.getElementById('data-transfer').textContent = (totalTransfer / 1024).toFixed(1);
            document.getElementById('uptime').textContent = uptime + 'm';
        }
        
        function updateNetworkTable() {
            const tbody = document.getElementById('network-tbody');
            tbody.innerHTML = networkData.adapters.map(adapter => `
                <tr>
                    <td>${adapter.name}</td>
                    <td>
                        <span class="status-indicator ${adapter.status === '已连接' ? 'online' : 'offline'}"></span>
                        ${adapter.status}
                    </td>
                    <td>${(adapter.received / 1024).toFixed(2)}</td>
                    <td>${(adapter.sent / 1024).toFixed(2)}</td>
                    <td>${((adapter.received + adapter.sent) / 1024).toFixed(2)}</td>
                    <td>${adapter.speed}</td>
                </tr>
            `).join('');
        }
        
        function updateConnectionStats() {
            const conn = networkData.connections;
            document.getElementById('connection-stats').innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div style="background: white; padding: 15px; border-radius: 8px;">
                        <strong>总连接数:</strong> ${conn.total}
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 8px;">
                        <strong>已建立:</strong> ${conn.established}
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 8px;">
                        <strong>监听中:</strong> ${conn.listening}
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 8px;">
                        <strong>等待关闭:</strong> ${conn.timeWait}
                    </div>
                </div>
            `;
        }
        
        function refreshData() {
            // 模拟数据变化
            networkData.adapters.forEach(adapter => {
                if (adapter.status === '已连接') {
                    adapter.received += Math.random() * 100;
                    adapter.sent += Math.random() * 50;
                }
            });
            
            networkData.connections.total += Math.floor(Math.random() * 10 - 5);
            networkData.connections.total = Math.max(0, networkData.connections.total);
            
            updateStats();
            updateNetworkTable();
            updateConnectionStats();
            
            document.getElementById('last-update').textContent = new Date().toLocaleString('zh-CN');
            
            console.log('数据已刷新');
        }
        
        function exportData() {
            const data = {
                timestamp: new Date().toISOString(),
                networkData: networkData,
                stats: {
                    totalAdapters: document.getElementById('total-adapters').textContent,
                    totalConnections: document.getElementById('total-connections').textContent,
                    dataTransfer: document.getElementById('data-transfer').textContent,
                    uptime: document.getElementById('uptime').textContent
                }
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `network_monitor_${new Date().toISOString().slice(0, 10)}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            alert('数据已导出到下载文件夹');
        }
        
        // 初始化
        function init() {
            updateTime();
            updateStats();
            updateNetworkTable();
            updateConnectionStats();
            
            // 定时更新
            setInterval(updateTime, 1000);
            setInterval(refreshData, 30000);
            
            console.log('网络监控系统已初始化');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
