# Docker 网络问题诊断和修复脚本
# 解决代理配置、镜像源、PATH等问题

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Docker 网络问题诊断修复工具" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 步骤 1: 检查 Docker 安装和 PATH 配置
Write-Host "步骤 1: 检查 Docker 安装状态..." -ForegroundColor Yellow

# 常见的 Docker 安装路径
$dockerPaths = @(
    "C:\Program Files\Docker\Docker\resources\bin\docker.exe",
    "C:\Program Files\Docker\Docker\Docker Desktop.exe",
    "$env:USERPROFILE\AppData\Local\Docker\Docker Desktop.exe",
    "$env:ProgramFiles\Docker\Docker\resources\bin\docker.exe"
)

$dockerExe = $null
$dockerDesktop = $null

foreach ($path in $dockerPaths) {
    if (Test-Path $path) {
        if ($path -like "*docker.exe") {
            $dockerExe = $path
            Write-Host "✅ 找到 Docker CLI: $path" -ForegroundColor Green
        } elseif ($path -like "*Docker Desktop.exe") {
            $dockerDesktop = $path
            Write-Host "✅ 找到 Docker Desktop: $path" -ForegroundColor Green
        }
    }
}

if (-not $dockerExe) {
    Write-Host "❌ 未找到 Docker CLI，正在搜索..." -ForegroundColor Red

    # 搜索整个系统
    $searchResult = Get-ChildItem -Path "C:\" -Recurse -Name "docker.exe" -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($searchResult) {
        $dockerExe = "C:\$searchResult"
        Write-Host "✅ 找到 Docker CLI: $dockerExe" -ForegroundColor Green
    } else {
        Write-Host "❌ 系统中未安装 Docker CLI" -ForegroundColor Red
        Write-Host "请先安装 Docker Desktop: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
        exit 1
    }
}

# 检查 Docker 进程
$dockerProcesses = Get-Process -Name "*docker*" -ErrorAction SilentlyContinue
if ($dockerProcesses) {
    Write-Host "✅ Docker 进程正在运行:" -ForegroundColor Green
    $dockerProcesses | ForEach-Object { Write-Host "  - $($_.ProcessName) (PID: $($_.Id))" -ForegroundColor Cyan }
} else {
    Write-Host "❌ Docker 进程未运行" -ForegroundColor Red
    if ($dockerDesktop) {
        Write-Host "正在启动 Docker Desktop..." -ForegroundColor Yellow
        Start-Process $dockerDesktop -WindowStyle Hidden
        Write-Host "等待 Docker Desktop 启动..." -ForegroundColor Cyan
        Start-Sleep -Seconds 30
    }
}

# 创建 Docker 命令函数
function Invoke-Docker {
    param([string]$Arguments)

    if ($dockerExe) {
        $cmd = "& `"$dockerExe`" $Arguments"
        return Invoke-Expression $cmd
    } else {
        throw "Docker CLI 不可用"
    }
}

Write-Host ""
Write-Host "步骤 2: 测试 Docker 连接..." -ForegroundColor Yellow

try {
    $dockerInfo = Invoke-Docker "info 2>&1"
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker 守护进程连接成功" -ForegroundColor Green

        # 检查代理配置
        $proxyInfo = $dockerInfo | Select-String -Pattern "Proxy"
        if ($proxyInfo) {
            Write-Host "⚠️ 检测到代理配置:" -ForegroundColor Yellow
            $proxyInfo | ForEach-Object { Write-Host "  $($_.Line)" -ForegroundColor Cyan }
        } else {
            Write-Host "✅ 未检测到代理配置" -ForegroundColor Green
        }
    } else {
        Write-Host "❌ Docker 守护进程连接失败" -ForegroundColor Red
        Write-Host "错误信息: $dockerInfo" -ForegroundColor Red

        Write-Host "尝试重启 Docker Desktop..." -ForegroundColor Yellow
        if ($dockerDesktop) {
            Stop-Process -Name "Docker Desktop" -Force -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 5
            Start-Process $dockerDesktop -WindowStyle Hidden
            Start-Sleep -Seconds 30
        }
    }
} catch {
    Write-Host "❌ Docker 命令执行失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "步骤 3: 配置 Docker 镜像源..." -ForegroundColor Yellow

# 检查当前镜像源配置
$daemonJsonPath = "$env:USERPROFILE\.docker\daemon.json"
$daemonJsonExists = Test-Path $daemonJsonPath

Write-Host "Docker 配置文件路径: $daemonJsonPath" -ForegroundColor Cyan

if ($daemonJsonExists) {
    Write-Host "✅ 找到现有配置文件" -ForegroundColor Green
    $currentConfig = Get-Content $daemonJsonPath -Raw | ConvertFrom-Json -ErrorAction SilentlyContinue
    if ($currentConfig -and $currentConfig.'registry-mirrors') {
        Write-Host "当前镜像源:" -ForegroundColor Cyan
        $currentConfig.'registry-mirrors' | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    }
} else {
    Write-Host "⚠️ 未找到配置文件，将创建新的配置" -ForegroundColor Yellow
}

# 推荐的镜像源配置
$recommendedConfig = @{
    "registry-mirrors" = @(
        "https://docker.mirrors.ustc.edu.cn",
        "https://hub-mirror.c.163.com",
        "https://mirror.baidubce.com",
        "https://dockerproxy.com",
        "https://docker.nju.edu.cn"
    )
    "insecure-registries" = @()
    "debug" = $false
    "experimental" = $false
    "log-level" = "info"
}

Write-Host ""
Write-Host "推荐的镜像源配置:" -ForegroundColor Yellow
$recommendedConfig.'registry-mirrors' | ForEach-Object { Write-Host "  - $_" -ForegroundColor Cyan }

$updateConfig = Read-Host "是否更新 Docker 镜像源配置? (y/N)"
if ($updateConfig -eq 'y' -or $updateConfig -eq 'Y') {
    try {
        # 确保目录存在
        $dockerConfigDir = Split-Path $daemonJsonPath -Parent
        if (-not (Test-Path $dockerConfigDir)) {
            New-Item -ItemType Directory -Path $dockerConfigDir -Force | Out-Null
        }

        # 写入新配置
        $recommendedConfig | ConvertTo-Json -Depth 3 | Set-Content $daemonJsonPath -Encoding UTF8
        Write-Host "✅ Docker 配置已更新" -ForegroundColor Green
        Write-Host "⚠️ 需要重启 Docker Desktop 以应用新配置" -ForegroundColor Yellow

        $restartDocker = Read-Host "是否立即重启 Docker Desktop? (y/N)"
        if ($restartDocker -eq 'y' -or $restartDocker -eq 'Y') {
            Write-Host "正在重启 Docker Desktop..." -ForegroundColor Cyan
            Stop-Process -Name "Docker Desktop" -Force -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 5
            if ($dockerDesktop) {
                Start-Process $dockerDesktop -WindowStyle Hidden
                Write-Host "等待 Docker Desktop 重启..." -ForegroundColor Cyan
                Start-Sleep -Seconds 45
            }
        }
    } catch {
        Write-Host "❌ 配置更新失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "步骤 4: 测试镜像拉取..." -ForegroundColor Yellow

# 测试镜像拉取
$testImages = @("hello-world:latest", "alpine:latest")
$pullSuccess = $false

foreach ($image in $testImages) {
    Write-Host "测试拉取镜像: $image" -ForegroundColor Cyan

    try {
        $pullResult = Invoke-Docker "pull $image 2>&1"
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 成功拉取 $image" -ForegroundColor Green
            $pullSuccess = $true
            break
        } else {
            Write-Host "❌ 拉取 $image 失败" -ForegroundColor Red
            Write-Host "错误信息: $pullResult" -ForegroundColor Gray
        }
    } catch {
        Write-Host "❌ 拉取 $image 异常: $($_.Exception.Message)" -ForegroundColor Red
    }

    Start-Sleep -Seconds 3
}

Write-Host ""
Write-Host "步骤 5: 网络连接测试..." -ForegroundColor Yellow

if (-not $pullSuccess) {
    Write-Host "尝试清除代理设置并重试..." -ForegroundColor Cyan

    # 清除代理环境变量
    $env:HTTP_PROXY = ""
    $env:HTTPS_PROXY = ""
    $env:http_proxy = ""
    $env:https_proxy = ""

    Write-Host "已清除代理环境变量" -ForegroundColor Cyan

    try {
        $directPullResult = Invoke-Docker "pull hello-world:latest 2>&1"
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 直接拉取成功!" -ForegroundColor Green
            $pullSuccess = $true
        } else {
            Write-Host "❌ 直接拉取仍然失败" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 直接拉取异常" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           诊断结果总结" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

if ($pullSuccess) {
    Write-Host "🎉 Docker 网络问题已解决！" -ForegroundColor Green
    Write-Host ""
    Write-Host "✅ Docker CLI 可用: $dockerExe" -ForegroundColor Green
    Write-Host "✅ Docker 守护进程运行正常" -ForegroundColor Green
    Write-Host "✅ 镜像拉取测试成功" -ForegroundColor Green
    Write-Host ""
    Write-Host "现在可以部署完整的监控系统了！" -ForegroundColor Green
    Write-Host ""
    Write-Host "部署命令:" -ForegroundColor Yellow
    Write-Host "  cd $PWD" -ForegroundColor Cyan
    Write-Host "  & `"$dockerExe`" compose up -d" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "或者运行启动脚本:" -ForegroundColor Yellow
    Write-Host "  .\start_monitor.bat" -ForegroundColor Cyan

} else {
    Write-Host "⚠️ Docker 网络问题仍然存在" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "可能的原因:" -ForegroundColor Red
    Write-Host "  - 网络防火墙阻止 Docker 访问" -ForegroundColor White
    Write-Host "  - 公司代理配置问题" -ForegroundColor White
    Write-Host "  - DNS 解析问题" -ForegroundColor White
    Write-Host "  - Docker Desktop 配置错误" -ForegroundColor White
    Write-Host ""
    Write-Host "建议的解决方案:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "方案 1: 手动配置 Docker Desktop" -ForegroundColor Cyan
    Write-Host "  1. 打开 Docker Desktop" -ForegroundColor White
    Write-Host "  2. 进入 Settings -> Resources -> Proxies" -ForegroundColor White
    Write-Host "  3. 配置或禁用代理设置" -ForegroundColor White
    Write-Host "  4. 进入 Settings -> Docker Engine" -ForegroundColor White
    Write-Host "  5. 添加镜像源配置 (已提供推荐配置)" -ForegroundColor White
    Write-Host ""
    Write-Host "方案 2: 使用本地监控替代方案" -ForegroundColor Cyan
    Write-Host "  运行: .\start_local_monitor.bat" -ForegroundColor White
    Write-Host ""
    Write-Host "方案 3: 手动安装各组件" -ForegroundColor Cyan
    Write-Host "  - InfluxDB: https://www.influxdata.com/downloads/" -ForegroundColor White
    Write-Host "  - Grafana: https://grafana.com/grafana/download" -ForegroundColor White
    Write-Host "  - sFlow-RT: https://sflow-rt.com/download.php" -ForegroundColor White
    Write-Host ""
    Write-Host "方案 4: 使用云服务" -ForegroundColor Cyan
    Write-Host "  - InfluxDB Cloud: https://cloud2.influxdata.com/" -ForegroundColor White
    Write-Host "  - Grafana Cloud: https://grafana.com/products/cloud/" -ForegroundColor White
}

Write-Host ""
Write-Host "配置文件位置:" -ForegroundColor Gray
Write-Host "  Docker 配置: $daemonJsonPath" -ForegroundColor Gray
Write-Host "  项目配置: $PWD\docker-compose.yml" -ForegroundColor Gray
Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
